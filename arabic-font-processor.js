/**
 * Arabic Font Processor for CNC Text Conversion
 * Handles Arabic text processing and dot merging for CNC cutting
 */

class ArabicFontProcessor {
    constructor() {
        // Arabic letters with dots that need merging - mapping to base forms without dots
        this.lettersWithDots = {
            'ب': { base: 'ٮ', dots: 1, position: 'below', replacement: 'ب' },
            'ت': { base: 'ٮ', dots: 2, position: 'above', replacement: 'ت' },
            'ث': { base: 'ٮ', dots: 3, position: 'above', replacement: 'ث' },
            'ج': { base: 'ح', dots: 1, position: 'below', replacement: 'ج' },
            'خ': { base: 'ح', dots: 1, position: 'above', replacement: 'خ' },
            'ذ': { base: 'د', dots: 1, position: 'above', replacement: 'ذ' },
            'ز': { base: 'ر', dots: 1, position: 'above', replacement: 'ز' },
            'ش': { base: 'س', dots: 3, position: 'above', replacement: 'ش' },
            'ض': { base: 'ص', dots: 1, position: 'above', replacement: 'ض' },
            'ظ': { base: 'ط', dots: 1, position: 'above', replacement: 'ظ' },
            'غ': { base: 'ع', dots: 1, position: 'above', replacement: 'غ' },
            'ف': { base: 'ڡ', dots: 1, position: 'above', replacement: 'ف' },
            'ق': { base: 'ڡ', dots: 2, position: 'above', replacement: 'ق' },
            'ن': { base: 'ں', dots: 1, position: 'above', replacement: 'ن' },
            'ي': { base: 'ى', dots: 2, position: 'below', replacement: 'ي' },
            'ة': { base: 'ه', dots: 2, position: 'above', replacement: 'ة' }
        };

        // Alternative base forms for better CNC compatibility
        this.baseForms = {
            'ب': 'ٮ', 'ت': 'ٮ', 'ث': 'ٮ',  // All use dotless beh
            'ج': 'ح', 'خ': 'ح',              // Use hah
            'ذ': 'د', 'ز': 'ر',              // Use dal and reh
            'ش': 'س', 'ض': 'ص', 'ظ': 'ط',   // Use seen, sad, tah
            'غ': 'ع', 'ف': 'ڡ', 'ق': 'ڡ',   // Use ain and dotless feh
            'ن': 'ں', 'ي': 'ى', 'ة': 'ه'    // Use dotless noon, alef maksura, heh
        };

        // Fallback mapping for characters that might not display properly
        this.fallbackBaseForms = {
            'ب': 'ٮ', 'ت': 'ٮ', 'ث': 'ٮ',
            'ج': 'ح', 'خ': 'ح',
            'ذ': 'د', 'ز': 'ر',
            'ش': 'س', 'ض': 'ص', 'ظ': 'ط',
            'غ': 'ع', 'ف': 'ف', 'ق': 'ق',  // Use regular forms if dotless not available
            'ن': 'ن', 'ي': 'ى', 'ة': 'ه'
        };

        // Diacritical marks (tashkeel)
        this.diacritics = [
            '\u064B', // Fathatan
            '\u064C', // Dammatan
            '\u064D', // Kasratan
            '\u064E', // Fatha
            '\u064F', // Damma
            '\u0650', // Kasra
            '\u0651', // Shadda
            '\u0652', // Sukun
            '\u0653', // Maddah
            '\u0654', // Hamza above
            '\u0655', // Hamza below
            '\u0656', // Subscript alef
            '\u0657', // Inverted damma
            '\u0658', // Mark noon ghunna
            '\u0659', // Zwarakay
            '\u065A', // Vowel sign small v above
            '\u065B', // Vowel sign inverted small v above
            '\u065C', // Vowel sign dot below
            '\u065D', // Reversed damma
            '\u065E', // Fatha with two dots
            '\u065F', // Wavy hamza below
            '\u0670'  // Superscript alef
        ];
    }

    /**
     * Process Arabic text for CNC cutting
     * @param {string} text - Input Arabic text
     * @param {boolean} mergeDots - Whether to merge dots with letters
     * @returns {object} Processed text data
     */
    processText(text, mergeDots = true) {
        if (!text || typeof text !== 'string') {
            return { originalText: '', processedText: '', cncText: '', letterData: [] };
        }

        const letterData = [];
        let processedText = text;

        // Normalize Arabic text
        processedText = this.normalizeArabicText(processedText);

        // Create CNC-ready text by converting letters to base forms
        let cncText = processedText;
        if (mergeDots) {
            cncText = this.convertToBaseForms(processedText);
        }

        // Process each character
        for (let i = 0; i < processedText.length; i++) {
            const char = processedText[i];
            const letterInfo = this.analyzeCharacter(char, i);
            letterData.push(letterInfo);
        }

        return {
            originalText: text,
            processedText: processedText,
            cncText: cncText,
            letterData: letterData,
            mergeDots: mergeDots
        };
    }

    /**
     * Convert Arabic text to base forms without dots
     * @param {string} text - Input text
     * @returns {string} Text with base forms
     */
    convertToBaseForms(text) {
        let result = '';
        for (let i = 0; i < text.length; i++) {
            const char = text[i];
            if (this.baseForms[char]) {
                result += this.baseForms[char];
            } else {
                result += char;
            }
        }
        return result;
    }

    /**
     * Normalize Arabic text
     * @param {string} text - Input text
     * @returns {string} Normalized text
     */
    normalizeArabicText(text) {
        // Remove extra whitespace
        text = text.trim().replace(/\s+/g, ' ');

        // Normalize Arabic characters
        text = text.replace(/ي/g, 'ي'); // Normalize yeh
        text = text.replace(/ك/g, 'ك'); // Normalize kaf

        return text;
    }

    /**
     * Analyze a single character
     * @param {string} char - Character to analyze
     * @param {number} position - Position in text
     * @returns {object} Character analysis
     */
    analyzeCharacter(char, position) {
        const charCode = char.charCodeAt(0);
        const isArabic = (charCode >= 0x0600 && charCode <= 0x06FF) ||
                        (charCode >= 0x0750 && charCode <= 0x077F);

        const letterInfo = {
            char: char,
            position: position,
            isArabic: isArabic,
            isDiacritic: this.diacritics.includes(char),
            hasDotsInfo: this.lettersWithDots[char] || null,
            needsMerging: false
        };

        // Check if this letter needs dot merging
        if (letterInfo.hasDotsInfo && letterInfo.hasDotsInfo.dots > 0) {
            letterInfo.needsMerging = true;
        }

        return letterInfo;
    }

    /**
     * Get font rendering instructions for CNC
     * @param {object} textData - Processed text data
     * @param {object} options - Rendering options
     * @returns {object} Rendering instructions
     */
    getCNCRenderingInstructions(textData, options = {}) {
        const defaultOptions = {
            fontSize: 48,
            fontFamily: 'Amiri',
            strokeWidth: 2,
            mergeDots: true,
            dotConnectionStrength: 0.8 // How strongly to connect dots (0-1)
        };

        const renderOptions = { ...defaultOptions, ...options };

        return {
            textData: textData,
            renderOptions: renderOptions,
            instructions: this.generateRenderingInstructions(textData, renderOptions)
        };
    }

    /**
     * Generate specific rendering instructions
     * @param {object} textData - Text data
     * @param {object} options - Options
     * @returns {array} Array of rendering instructions
     */
    generateRenderingInstructions(textData, options) {
        const instructions = [];

        textData.letterData.forEach((letterInfo, index) => {
            if (letterInfo.isArabic && !letterInfo.isDiacritic) {
                const instruction = {
                    type: 'letter',
                    char: letterInfo.char,
                    position: index,
                    needsMerging: letterInfo.needsMerging,
                    dotInfo: letterInfo.hasDotsInfo,
                    renderMethod: letterInfo.needsMerging && options.mergeDots ? 'merged' : 'standard'
                };

                instructions.push(instruction);
            } else if (letterInfo.isDiacritic) {
                instructions.push({
                    type: 'diacritic',
                    char: letterInfo.char,
                    position: index,
                    renderMethod: 'overlay'
                });
            } else {
                instructions.push({
                    type: 'other',
                    char: letterInfo.char,
                    position: index,
                    renderMethod: 'standard'
                });
            }
        });

        return instructions;
    }

    /**
     * Calculate dot merge positions as small lines instead of circles
     * @param {object} letterBounds - Letter bounding box
     * @param {object} dotInfo - Dot information
     * @param {number} fontSize - Font size
     * @returns {array} Array of line positions
     */
    calculateDotMergePositions(letterBounds, dotInfo, fontSize) {
        if (!dotInfo || dotInfo.dots === 0) return [];

        const positions = [];
        const lineLength = fontSize * 0.15; // Line length relative to font size
        const lineWidth = fontSize * 0.03; // Line width
        const spacing = fontSize * 0.12; // Spacing between lines

        const baseX = letterBounds.x + letterBounds.width / 2;
        let baseY;

        // Calculate Y position based on dot position
        if (dotInfo.position === 'above') {
            baseY = letterBounds.y - fontSize * 0.08;
        } else if (dotInfo.position === 'below') {
            baseY = letterBounds.y + letterBounds.height + fontSize * 0.03;
        } else {
            baseY = letterBounds.y + letterBounds.height / 2;
        }

        // Calculate positions for multiple lines
        const totalWidth = (dotInfo.dots - 1) * spacing;
        const startX = baseX - totalWidth / 2;

        for (let i = 0; i < dotInfo.dots; i++) {
            const lineX = startX + i * spacing;
            const connectionPoint = this.calculateConnectionPoint(letterBounds, lineX, baseY, dotInfo.position);

            positions.push({
                type: 'line',
                startX: lineX - lineLength / 2,
                startY: baseY,
                endX: lineX + lineLength / 2,
                endY: baseY,
                width: lineWidth,
                connectionPoint: connectionPoint,
                // Connection line from letter to the small line
                connectionLine: {
                    startX: connectionPoint.x,
                    startY: connectionPoint.y,
                    endX: lineX,
                    endY: baseY
                }
            });
        }

        return positions;
    }

    /**
     * Calculate connection point between dot and letter
     * @param {object} letterBounds - Letter bounds
     * @param {number} dotX - Dot X position
     * @param {number} dotY - Dot Y position
     * @param {string} position - Dot position (above/below)
     * @returns {object} Connection point
     */
    calculateConnectionPoint(letterBounds, dotX, dotY, position) {
        let connectionX = dotX;
        let connectionY;

        if (position === 'above') {
            connectionY = letterBounds.y;
        } else if (position === 'below') {
            connectionY = letterBounds.y + letterBounds.height;
        } else {
            connectionY = letterBounds.y + letterBounds.height / 2;
        }

        // Ensure connection point is within letter bounds
        connectionX = Math.max(letterBounds.x, Math.min(letterBounds.x + letterBounds.width, connectionX));

        return { x: connectionX, y: connectionY };
    }

    /**
     * Remove diacritics from text
     * @param {string} text - Input text
     * @returns {string} Text without diacritics
     */
    removeDiacritics(text) {
        let cleanText = text;
        this.diacritics.forEach(diacritic => {
            cleanText = cleanText.replace(new RegExp(diacritic, 'g'), '');
        });
        return cleanText;
    }

    /**
     * Check if text contains Arabic characters
     * @param {string} text - Input text
     * @returns {boolean} True if contains Arabic
     */
    containsArabic(text) {
        const arabicRegex = /[\u0600-\u06FF\u0750-\u077F]/;
        return arabicRegex.test(text);
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ArabicFontProcessor;
} else {
    window.ArabicFontProcessor = ArabicFontProcessor;
}
