/**
 * Main Application Script for Arabic CNC Text Converter
 */

class ArabicCNCConverter {
    constructor() {
        this.processor = new ArabicFontProcessor();
        this.canvas = null;
        this.ctx = null;
        this.currentTextData = null;
        this.currentRenderInstructions = null;

        this.initializeElements();
        this.bindEvents();
        this.setupCanvas();

        // Set default text
        this.elements.arabicText.value = 'بُورِكَت فَاتِحَة العُمْر معك';
        this.updatePreview();
    }

    initializeElements() {
        this.elements = {
            arabicText: document.getElementById('arabicText'),
            fontSize: document.getElementById('fontSize'),
            fontSizeValue: document.getElementById('fontSizeValue'),
            fontFamily: document.getElementById('fontFamily'),
            dotMerging: document.getElementById('dotMerging'),
            strokeWidth: document.getElementById('strokeWidth'),
            strokeWidthValue: document.getElementById('strokeWidthValue'),
            canvas: document.getElementById('textCanvas'),
            exportPNG: document.getElementById('exportPNG'),
            exportSVG: document.getElementById('exportSVG'),
            clearCanvas: document.getElementById('clearCanvas')
        };
    }

    bindEvents() {
        // Text input events
        this.elements.arabicText.addEventListener('input', () => this.updatePreview());
        this.elements.arabicText.addEventListener('paste', () => {
            setTimeout(() => this.updatePreview(), 10);
        });

        // Control events
        this.elements.fontSize.addEventListener('input', (e) => {
            this.elements.fontSizeValue.textContent = e.target.value + 'px';
            this.updatePreview();
        });

        this.elements.fontFamily.addEventListener('change', () => this.updatePreview());
        this.elements.dotMerging.addEventListener('change', () => this.updatePreview());

        this.elements.strokeWidth.addEventListener('input', (e) => {
            this.elements.strokeWidthValue.textContent = e.target.value + 'px';
            this.updatePreview();
        });

        // Export events
        this.elements.exportPNG.addEventListener('click', () => this.exportAsPNG());
        this.elements.exportSVG.addEventListener('click', () => this.exportAsSVG());
        this.elements.clearCanvas.addEventListener('click', () => this.clearCanvas());

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 's':
                        e.preventDefault();
                        this.exportAsPNG();
                        break;
                    case 'e':
                        e.preventDefault();
                        this.exportAsSVG();
                        break;
                }
            }
        });
    }

    setupCanvas() {
        this.canvas = this.elements.canvas;
        this.ctx = this.canvas.getContext('2d');

        // Set high DPI support
        const dpr = window.devicePixelRatio || 1;
        const rect = this.canvas.getBoundingClientRect();

        this.canvas.width = rect.width * dpr;
        this.canvas.height = rect.height * dpr;

        this.ctx.scale(dpr, dpr);
        this.canvas.style.width = rect.width + 'px';
        this.canvas.style.height = rect.height + 'px';
    }

    updatePreview() {
        const text = this.elements.arabicText.value.trim();

        if (!text) {
            this.clearCanvas();
            return;
        }

        // Process the text
        const mergeDots = this.elements.dotMerging.checked;
        this.currentTextData = this.processor.processText(text, mergeDots);

        // Get rendering options
        const renderOptions = {
            fontSize: parseInt(this.elements.fontSize.value),
            fontFamily: this.elements.fontFamily.value,
            strokeWidth: parseFloat(this.elements.strokeWidth.value),
            mergeDots: mergeDots
        };

        // Get rendering instructions
        this.currentRenderInstructions = this.processor.getCNCRenderingInstructions(
            this.currentTextData,
            renderOptions
        );

        // Render on canvas
        this.renderToCanvas();
    }

    renderToCanvas() {
        if (!this.currentTextData || !this.currentRenderInstructions) return;

        const ctx = this.ctx;
        const canvas = this.canvas;
        const options = this.currentRenderInstructions.renderOptions;

        // Clear canvas
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // Set canvas background
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // Configure text rendering
        ctx.font = `${options.fontSize}px ${options.fontFamily}`;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillStyle = '#000000';
        ctx.strokeStyle = '#000000';
        ctx.lineWidth = options.strokeWidth;

        // Calculate text position
        const centerX = canvas.width / 2;
        const centerY = canvas.height / 2;

        if (options.mergeDots) {
            this.renderWithMergedDots(ctx, centerX, centerY, options);
        } else {
            this.renderStandardText(ctx, centerX, centerY, options);
        }
    }

    renderStandardText(ctx, x, y, options) {
        const text = this.currentTextData.processedText;

        // Render text outline for CNC
        ctx.strokeText(text, x, y);

        // Optional: fill text for preview
        ctx.globalAlpha = 0.3;
        ctx.fillText(text, x, y);
        ctx.globalAlpha = 1.0;
    }

    renderWithMergedDots(ctx, centerX, centerY, options) {
        const text = this.currentTextData.processedText;

        // First, render the base text without dots
        const textWithoutDiacritics = this.processor.removeDiacritics(text);

        // Measure text to get bounds
        const textMetrics = ctx.measureText(textWithoutDiacritics);
        const textWidth = textMetrics.width;
        const textHeight = options.fontSize;

        // Render base text
        ctx.strokeText(textWithoutDiacritics, centerX, centerY);
        ctx.globalAlpha = 0.3;
        ctx.fillText(textWithoutDiacritics, centerX, centerY);
        ctx.globalAlpha = 1.0;

        // Calculate letter positions and render merged dots
        this.renderMergedDots(ctx, centerX, centerY, textWidth, textHeight, options);
    }

    renderMergedDots(ctx, centerX, centerY, textWidth, textHeight, options) {
        const instructions = this.currentRenderInstructions.instructions;
        const fontSize = options.fontSize;

        // Estimate character width (this is approximate)
        const avgCharWidth = textWidth / this.currentTextData.processedText.length;
        const startX = centerX - textWidth / 2;

        let currentX = startX;

        instructions.forEach((instruction, index) => {
            if (instruction.type === 'letter' && instruction.needsMerging && instruction.dotInfo) {
                const letterBounds = {
                    x: currentX,
                    y: centerY - textHeight / 2,
                    width: avgCharWidth,
                    height: textHeight
                };

                const dotPositions = this.processor.calculateDotMergePositions(
                    letterBounds,
                    instruction.dotInfo,
                    fontSize
                );

                // Render dots and connections
                dotPositions.forEach(dotPos => {
                    // Draw connection line from letter to dot
                    ctx.beginPath();
                    ctx.moveTo(dotPos.connectionPoint.x, dotPos.connectionPoint.y);
                    ctx.lineTo(dotPos.x, dotPos.y);
                    ctx.stroke();

                    // Draw dot
                    ctx.beginPath();
                    ctx.arc(dotPos.x, dotPos.y, dotPos.size, 0, 2 * Math.PI);
                    ctx.stroke();
                    ctx.globalAlpha = 0.3;
                    ctx.fill();
                    ctx.globalAlpha = 1.0;
                });
            }

            // Move to next character position
            if (instruction.type === 'letter' || instruction.type === 'other') {
                currentX += avgCharWidth;
            }
        });
    }

    clearCanvas() {
        if (this.ctx) {
            this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
            this.ctx.fillStyle = '#ffffff';
            this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        }
    }

    exportAsPNG() {
        if (!this.canvas) return;

        try {
            const link = document.createElement('a');
            link.download = `arabic-cnc-design-${Date.now()}.png`;
            link.href = this.canvas.toDataURL('image/png');
            link.click();

            this.showNotification('تم تصدير الملف بنجاح! / PNG exported successfully!', 'success');
        } catch (error) {
            console.error('Export error:', error);
            this.showNotification('خطأ في التصدير / Export error', 'error');
        }
    }

    exportAsSVG() {
        if (!this.currentTextData || !this.currentRenderInstructions) return;

        try {
            const svg = this.generateSVG();
            const blob = new Blob([svg], { type: 'image/svg+xml' });
            const url = URL.createObjectURL(blob);

            const link = document.createElement('a');
            link.download = `arabic-cnc-design-${Date.now()}.svg`;
            link.href = url;
            link.click();

            URL.revokeObjectURL(url);
            this.showNotification('تم تصدير ملف SVG بنجاح! / SVG exported successfully!', 'success');
        } catch (error) {
            console.error('SVG export error:', error);
            this.showNotification('خطأ في تصدير SVG / SVG export error', 'error');
        }
    }

    generateSVG() {
        const options = this.currentRenderInstructions.renderOptions;
        const text = this.currentTextData.processedText;
        const width = 800;
        const height = 400;

        let svg = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
<defs>
<style>
.arabic-text {
    font-family: ${options.fontFamily};
    font-size: ${options.fontSize}px;
    fill: none;
    stroke: black;
    stroke-width: ${options.strokeWidth}px;
    text-anchor: middle;
    dominant-baseline: middle;
}
.dot {
    fill: none;
    stroke: black;
    stroke-width: ${options.strokeWidth}px;
}
.connection {
    stroke: black;
    stroke-width: ${options.strokeWidth * 0.8}px;
}
</style>
</defs>
<rect width="100%" height="100%" fill="white"/>`;

        const centerX = width / 2;
        const centerY = height / 2;

        if (options.mergeDots) {
            const textWithoutDiacritics = this.processor.removeDiacritics(text);
            svg += `<text x="${centerX}" y="${centerY}" class="arabic-text">${this.escapeXML(textWithoutDiacritics)}</text>`;

            // Add dots and connections (simplified for SVG)
            const instructions = this.currentRenderInstructions.instructions;
            const avgCharWidth = 400 / text.length; // Approximate
            let currentX = centerX - 200;

            instructions.forEach(instruction => {
                if (instruction.type === 'letter' && instruction.needsMerging && instruction.dotInfo) {
                    const letterBounds = {
                        x: currentX,
                        y: centerY - options.fontSize / 2,
                        width: avgCharWidth,
                        height: options.fontSize
                    };

                    const dotPositions = this.processor.calculateDotMergePositions(
                        letterBounds,
                        instruction.dotInfo,
                        options.fontSize
                    );

                    dotPositions.forEach(dotPos => {
                        svg += `<line x1="${dotPos.connectionPoint.x}" y1="${dotPos.connectionPoint.y}" x2="${dotPos.x}" y2="${dotPos.y}" class="connection"/>`;
                        svg += `<circle cx="${dotPos.x}" cy="${dotPos.y}" r="${dotPos.size}" class="dot"/>`;
                    });
                }

                if (instruction.type === 'letter' || instruction.type === 'other') {
                    currentX += avgCharWidth;
                }
            });
        } else {
            svg += `<text x="${centerX}" y="${centerY}" class="arabic-text">${this.escapeXML(text)}</text>`;
        }

        svg += '</svg>';
        return svg;
    }

    escapeXML(text) {
        return text
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#39;');
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;

        // Style the notification
        Object.assign(notification.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            padding: '15px 20px',
            borderRadius: '8px',
            color: 'white',
            fontWeight: 'bold',
            zIndex: '10000',
            transform: 'translateX(100%)',
            transition: 'transform 0.3s ease'
        });

        if (type === 'success') {
            notification.style.backgroundColor = '#28a745';
        } else if (type === 'error') {
            notification.style.backgroundColor = '#dc3545';
        } else {
            notification.style.backgroundColor = '#17a2b8';
        }

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 10);

        // Remove after delay
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.arabicConverter = new ArabicCNCConverter();
});

// Handle window resize
window.addEventListener('resize', () => {
    // Reinitialize canvas on resize
    setTimeout(() => {
        if (window.arabicConverter) {
            window.arabicConverter.setupCanvas();
            window.arabicConverter.updatePreview();
        }
    }, 100);
});
