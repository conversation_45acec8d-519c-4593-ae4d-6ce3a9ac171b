# Arabic Text to CNC Design Converter
## محول النص العربي لتصميم CNC

A web-based application that transforms Arabic text into designs suitable for CNC cutting by converting letters to their base forms and replacing dots with small connected lines, ensuring dots won't fall off during cutting while maintaining Arabic script beauty.

## Features / الميزات

### Core Functionality
- **Arabic Text Processing**: Accepts Arabic text input with full Unicode support
- **Dot Merging**: Automatically merges diacritical dots (نقاط) with base letters to prevent them from falling off during CNC cutting
- **Real-time Preview**: Live canvas rendering as you type
- **Multiple Export Formats**: Export as PNG or SVG files
- **Font Selection**: Choose from multiple Arabic fonts optimized for CNC cutting

### Supported Letters with Dots
The application handles the following Arabic letters with diacritical dots:
- ب (1 dot below)
- ت (2 dots above)
- ث (3 dots above)
- ج (1 dot below)
- خ (1 dot above)
- ذ (1 dot above)
- ز (1 dot above)
- ش (3 dots above)
- ض (1 dot above)
- ظ (1 dot above)
- غ (1 dot above)
- ف (1 dot above)
- ق (2 dots above)
- ن (1 dot above)
- ي (2 dots below)
- ة (2 dots above)

### Technical Features
- **Responsive Design**: Works on desktop and mobile devices
- **High DPI Support**: Crisp rendering on high-resolution displays
- **Keyboard Shortcuts**:
  - Ctrl+S: Export PNG
  - Ctrl+E: Export SVG
- **RTL Support**: Proper right-to-left text handling
- **Modern Web Technologies**: HTML5 Canvas, ES6+ JavaScript, CSS Grid/Flexbox

## Usage / الاستخدام

### Getting Started
1. Open `index.html` in a modern web browser
2. Enter Arabic text in the input field
3. Adjust settings as needed:
   - Font size (حجم الخط)
   - Font family (نوع الخط)
   - Enable/disable dot merging (دمج النقاط)
   - Stroke width (سمك الخط)
4. Preview the result in real-time
5. Export as PNG or SVG when satisfied

### Example
**Input**: `بُورِكَت فَاتِحَة العُمْر معك`
**Output**: `ٮورڪٮ ڡاٮحه العمر معڪ` with small lines replacing dots and connected to base letters

### How It Works
1. **Letter Conversion**: Converts dotted letters to their base forms (ب → ٮ, ت → ٮ, ن → ں)
2. **Dot Replacement**: Replaces dots with small horizontal lines
3. **Connection**: Connects the small lines to the base letters with thin connecting lines
4. **CNC Ready**: Ensures no separate elements that could fall off during cutting

### Export Options
- **PNG**: Raster image suitable for immediate use
- **SVG**: Vector format ideal for CNC machines and further editing

## File Structure / هيكل الملفات

```
REBOT/
├── index.html              # Main HTML file
├── styles.css              # CSS styling
├── script.js               # Main application logic
├── arabic-font-processor.js # Arabic text processing engine
└── README.md               # This documentation
```

## Technical Implementation

### Architecture
The application follows a modular architecture:

1. **ArabicFontProcessor Class**: Handles Arabic text analysis and dot merging logic
2. **ArabicCNCConverter Class**: Manages UI interactions and canvas rendering
3. **Canvas Rendering**: Uses HTML5 Canvas for precise control over text and dot positioning
4. **SVG Generation**: Creates vector graphics for CNC compatibility

### Key Algorithms
- **Text Normalization**: Standardizes Arabic characters
- **Dot Detection**: Identifies letters that require dot merging
- **Position Calculation**: Determines optimal connection points between dots and letters
- **Rendering Pipeline**: Separates base text rendering from dot overlay

### Browser Compatibility
- Modern browsers with HTML5 Canvas support
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Customization / التخصيص

### Adding New Fonts
To add new Arabic fonts:
1. Include the font in the HTML head section
2. Add the font option to the font family select element
3. Ensure the font supports Arabic Unicode ranges

### Modifying Dot Merging
The dot merging algorithm can be customized in `arabic-font-processor.js`:
- Adjust `dotConnectionStrength` for connection intensity
- Modify `calculateDotMergePositions()` for different positioning
- Update `lettersWithDots` object to support additional characters

### Styling
Customize the appearance by modifying `styles.css`:
- Color schemes
- Layout adjustments
- Animation effects
- Responsive breakpoints

## Development / التطوير

### Local Development
1. Clone or download the project
2. Open `index.html` in a web browser
3. No build process required - pure client-side application

### Testing
Test with various Arabic texts including:
- Simple words with dots
- Complex sentences with diacritics
- Mixed Arabic and Latin text
- Long paragraphs

### Contributing
Contributions are welcome! Areas for improvement:
- Enhanced dot positioning algorithms
- Additional Arabic font support
- Better mobile responsiveness
- Performance optimizations

## License / الترخيص

This project is open source and available under the MIT License.

## Support / الدعم

For issues or questions:
1. Check the browser console for error messages
2. Ensure you're using a modern browser
3. Verify Arabic text input is properly encoded

## Future Enhancements / التحسينات المستقبلية

- [ ] Advanced typography controls
- [ ] Batch processing for multiple texts
- [ ] Integration with CAD software
- [ ] Custom dot connection styles
- [ ] Print-ready templates
- [ ] Multi-language support

---

Built with ❤️ for Arabic Typography and CNC Manufacturing
