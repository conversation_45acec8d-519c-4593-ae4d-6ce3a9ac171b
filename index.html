<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Arabic Text to CNC Design Converter</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Scheherazade+New:wght@400;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header>
            <h1>محول النص العربي لتصميم CNC</h1>
            <p>Arabic Text to CNC Design Converter</p>
        </header>

        <main>
            <div class="input-section">
                <div class="controls">
                    <div class="control-group">
                        <label for="arabicText">النص العربي:</label>
                        <textarea 
                            id="arabicText" 
                            placeholder="أدخل النص العربي هنا... مثال: بُورِكَت فَاتِحَة العُمْر معك"
                            rows="4"
                        ></textarea>
                    </div>

                    <div class="control-group">
                        <label for="fontSize">حجم الخط:</label>
                        <input type="range" id="fontSize" min="20" max="120" value="48">
                        <span id="fontSizeValue">48px</span>
                    </div>

                    <div class="control-group">
                        <label for="fontFamily">نوع الخط:</label>
                        <select id="fontFamily">
                            <option value="Amiri">Amiri</option>
                            <option value="Scheherazade New">Scheherazade New</option>
                            <option value="Arial">Arial</option>
                            <option value="Tahoma">Tahoma</option>
                        </select>
                    </div>

                    <div class="control-group">
                        <label for="dotMerging">دمج النقاط:</label>
                        <input type="checkbox" id="dotMerging" checked>
                        <span>تفعيل دمج النقاط مع الحروف</span>
                    </div>

                    <div class="control-group">
                        <label for="strokeWidth">سمك الخط:</label>
                        <input type="range" id="strokeWidth" min="1" max="5" value="2" step="0.5">
                        <span id="strokeWidthValue">2px</span>
                    </div>
                </div>
            </div>

            <div class="preview-section">
                <h3>المعاينة / Preview</h3>
                <div class="canvas-container">
                    <canvas id="textCanvas" width="800" height="400"></canvas>
                </div>
                
                <div class="export-controls">
                    <button id="exportPNG" class="btn btn-primary">
                        <span>📥</span> تصدير PNG
                    </button>
                    <button id="exportSVG" class="btn btn-secondary">
                        <span>📄</span> تصدير SVG
                    </button>
                    <button id="clearCanvas" class="btn btn-outline">
                        <span>🗑️</span> مسح
                    </button>
                </div>
            </div>

            <div class="info-section">
                <h3>معلومات التطبيق</h3>
                <div class="info-grid">
                    <div class="info-card">
                        <h4>الهدف</h4>
                        <p>تحويل النص العربي إلى تصميم مناسب لقطع CNC مع دمج النقاط التشكيلية</p>
                    </div>
                    <div class="info-card">
                        <h4>الميزات</h4>
                        <ul>
                             <li>دمج النقاط مع الحروف</li>
                            <li>تصدير PNG و SVG</li>
                            <li>خطوط عربية متعددة</li>
                            <li>معاينة فورية</li>
                        </ul>
                    </div>
                    <div class="info-card">
                        <h4>الاستخدام</h4>
                        <p>أدخل النص العربي، اختر الإعدادات المناسبة، ثم صدّر التصميم</p>
                    </div>
                </div>
            </div>
        </main>

        <footer>
            <p>&copy; 2024 Arabic CNC Text Converter - Built with ❤️ for Arabic Typography</p>
        </footer>
    </div>

    <script src="arabic-font-processor.js"></script>
    <script src="script.js"></script>
</body>
</html>
